using UnityEngine;
using UnityEngine.UIElements;
using System.Collections;

[System.Serializable]
public class DialogueData
{
    public string characterName;
    [TextArea(3, 10)]
    public string dialogueText;
    public string backgroundImage;
    public string characterImage;
    public string characterPosition = "center";
    public float autoDelay = 3f;
    public string audioClip;
    public bool isChoice = false;
    public string[] choices;
    public int[] choiceTargets;
}

public class DialogueSystem : MonoBehaviour
{
    [Header("Dialogue Data")]
    public DialogueData[] dialogues;
    
    [Header("Settings")]
    public float textSpeed = 0.05f;
    public bool autoMode = false;
    
    private UIDocument uiDocument;
    private VisualElement root;
    private Label dialogueText;
    private Label characterName;
    private Button nextButton;
    private VisualElement choicesContainer;
    
    private int currentDialogueIndex = 0;
    private bool isTyping = false;
    private Coroutine typingCoroutine;
    private Coroutine autoCoroutine;

    private void OnEnable()
    {
        InitializeUI();
    }

    private void InitializeUI()
    {
        uiDocument = GetComponent<UIDocument>();
        if (uiDocument != null)
        {
            root = uiDocument.rootVisualElement;
            dialogueText = root.Q<Label>("dialogue-text");
            characterName = root.Q<Label>("character-name");
            nextButton = root.Q<Button>("next-button");
            choicesContainer = root.Q("choices-container");
            
            if (nextButton != null)
            {
                nextButton.clicked += OnNextButtonClicked;
            }
        }
    }

    public void StartDialogue()
    {
        if (dialogues != null && dialogues.Length > 0)
        {
            currentDialogueIndex = 0;
            ShowDialogue(dialogues[currentDialogueIndex]);
        }
    }

    public void OnNextButtonClicked()
    {
        if (isTyping)
        {
            // Skip typing animation
            if (typingCoroutine != null)
            {
                StopCoroutine(typingCoroutine);
                isTyping = false;
                dialogueText.text = dialogues[currentDialogueIndex].dialogueText;
            }
        }
        else
        {
            NextDialogue();
        }
    }

    private void NextDialogue()
    {
        currentDialogueIndex++;
        if (currentDialogueIndex < dialogues.Length)
        {
            ShowDialogue(dialogues[currentDialogueIndex]);
        }
        else
        {
            Debug.Log("Story completed!");
        }
    }

    private void ShowDialogue(DialogueData dialogue)
    {
        if (characterName != null)
            characterName.text = dialogue.characterName;
        
        if (dialogueText != null)
        {
            if (typingCoroutine != null)
                StopCoroutine(typingCoroutine);
            
            typingCoroutine = StartCoroutine(TypeText(dialogue.dialogueText));
        }
    }

    private IEnumerator TypeText(string text)
    {
        isTyping = true;
        dialogueText.text = "";
        
        foreach (char c in text)
        {
            dialogueText.text += c;
            yield return new WaitForSeconds(textSpeed);
        }
        
        isTyping = false;
        
        if (autoMode)
        {
            autoCoroutine = StartCoroutine(AutoAdvance());
        }
    }

    private IEnumerator AutoAdvance()
    {
        yield return new WaitForSeconds(dialogues[currentDialogueIndex].autoDelay);
        NextDialogue();
    }

    public void ToggleAutoMode()
    {
        autoMode = !autoMode;
        Debug.Log($"Auto mode: {(autoMode ? "ON" : "OFF")}");
    }
}


